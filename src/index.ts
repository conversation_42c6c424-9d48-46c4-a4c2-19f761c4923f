/**
 * 应用程序入口文件
 * 启动Express服务器和初始化应用
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import passport from 'passport';
import cookieParser from 'cookie-parser';
import { config } from '@/config';
import { logger } from '@/config/logger';
import { connectDatabase, checkDatabaseHealth } from '@/config/database';
import { redisService } from '@/services/redis.service';
import { securityConfig, validateSecurityConfig } from '@/config/security';
import {
  securityHeaders,
  ipFilter,
  requestSizeLimit,
  suspiciousActivityDetection,
  securityAudit,
  apiRateLimit,
  requestSlowDown
} from '@/middleware/security.middleware';
import authRoutes from '@/routes/auth.routes';
import userRoutes from '@/routes/user.routes';
import oauthRoutes from '@/routes/oauth.routes';
import gatewayRoutes from '@/routes/gateway.routes';
import jwksRoutes from '@/routes/jwks.routes';
import uiRoutes from '@/routes/ui.routes';
import apiDocsRoutes from '@/routes/api-docs.routes';
// Passport策略将在Redis初始化后导入

// Prometheus监控
import { prometheusMiddleware } from '@/middleware/prometheus.middleware';

// 国际化服务
import { i18nService } from '@/services/i18n.service';
import { i18nMiddleware, rtlSupportMiddleware } from '@/middleware/i18n.middleware';

/**
 * 创建Express应用
 */
const app = express();

/**
 * 验证安全配置
 */
validateSecurityConfig();

/**
 * 安全中间件配置
 */
// 基础安全头
app.use(securityHeaders);

// IP过滤
app.use(ipFilter);

// 请求大小限制
app.use(requestSizeLimit);

// 可疑活动检测
app.use(suspiciousActivityDetection);

// 安全审计
app.use(securityAudit);

// Helmet安全头
app.use(helmet({
  contentSecurityPolicy: {
    directives: securityConfig.csp.directives,
    reportOnly: securityConfig.csp.reportOnly
  },
  crossOriginEmbedderPolicy: false,
  hsts: config.server.nodeEnv === 'production' ? {
    maxAge: securityConfig.headers.hsts.maxAge,
    includeSubDomains: securityConfig.headers.hsts.includeSubDomains,
    preload: securityConfig.headers.hsts.preload
  } : false
}));

/**
 * CORS配置 - 使用安全配置
 */
app.use(cors({
  origin: function (origin, callback) {
    // 开发环境允许所有源
    if (config.server.nodeEnv === 'development') {
      return callback(null, true);
    }

    // 检查源是否在允许列表中
    if (!origin || securityConfig.cors.allowedOrigins.includes(origin)) {
      return callback(null, true);
    }

    logger.warn('CORS源被拒绝', { origin, allowedOrigins: securityConfig.cors.allowedOrigins });
    callback(new Error('不允许的CORS源'));
  },
  credentials: securityConfig.cors.credentials,
  methods: securityConfig.cors.methods,
  allowedHeaders: securityConfig.cors.allowedHeaders,
  exposedHeaders: securityConfig.cors.exposedHeaders,
  maxAge: securityConfig.cors.maxAge
}));

/**
 * 基础中间件
 */
// Cookie解析中间件 - 必须在国际化中间件之前
app.use(cookieParser());

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

/**
 * 国际化中间件
 */
app.use(i18nMiddleware());
app.use(rtlSupportMiddleware());

/**
 * 速率限制中间件
 */
// 请求减速 (在达到限制前先减速)
app.use(requestSlowDown);

// API通用速率限制
app.use('/api/', apiRateLimit);

// 初始化Passport
app.use(passport.initialize());

/**
 * 监控中间件
 */
// Prometheus指标收集
app.use(prometheusMiddleware);

/**
 * 信任代理（用于获取真实IP）
 */
app.set('trust proxy', 1);

/**
 * 性能监控中间件
 */
import { responseTimeMiddleware, initializePerformanceMonitoring } from './middleware/performance.middleware';
app.use(responseTimeMiddleware);

/**
 * 请求日志中间件
 */
import { httpLogMiddleware, errorLogMiddleware } from './middleware/log-collection.middleware';
app.use(httpLogMiddleware);

// 保留原有的简单日志记录作为备份
app.use((req, res, next) => {
  const start = Date.now();

  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.info('HTTP请求', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
  });

  next();
});

/**
 * 健康检查端点
 */
app.get('/health', async (_req, res) => {
  try {
    const dbHealth = await checkDatabaseHealth();
    const redisHealth = redisService.isReady();

    const health = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      database: dbHealth ? 'connected' : 'disconnected',
      redis: redisHealth ? 'connected' : 'disconnected',
      memory: process.memoryUsage(),
      version: process.version
    };

    const overallHealth = dbHealth && redisHealth;
    res.status(overallHealth ? 200 : 503).json(health);
  } catch (error) {
    logger.error('健康检查失败', { error });
    res.status(503).json({
      status: 'error',
      message: '服务不可用'
    });
  }
});

/**
 * API路由
 */
// API版本的健康检查端点
app.get('/api/v1/health', async (_req, res) => {
  try {
    const dbHealth = await checkDatabaseHealth();
    const redisHealth = redisService.isReady();

    const health = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      database: dbHealth ? 'connected' : 'disconnected',
      redis: redisHealth ? 'connected' : 'disconnected',
      memory: process.memoryUsage(),
      version: process.version,
      service: 'id-provider',
      apiVersion: 'v1'
    };

    const overallHealth = dbHealth && redisHealth;
    res.status(overallHealth ? 200 : 503).json(health);
  } catch (error) {
    logger.error('API健康检查失败', { error });
    res.status(503).json({
      status: 'error',
      message: '服务不可用',
      apiVersion: 'v1'
    });
  }
});

// API 文档路由（无需认证）
app.use('/api', apiDocsRoutes);

app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/auth', oauthRoutes); // OAuth路由
app.use('/api/v1/me', userRoutes);
app.use('/api/v1/gateway', gatewayRoutes); // 网关集成路由

// JWKS和OpenID Connect发现端点 (原有实现)
app.use('/legacy', jwksRoutes);

// 标准化协议端点
import protocolEndpointsRoutes from './routes/protocol-endpoints.routes';
app.use('/', protocolEndpointsRoutes);

// 元数据管理路由
import metadataRoutes from './routes/metadata.routes';
app.use('/metadata', metadataRoutes);

// 权限管理路由
import permissionManagementRoutes from './routes/permission-management.routes';
app.use('/permissions', permissionManagementRoutes);

// 管理员API路由
import adminApiRoutes from './routes/admin-api.routes';
app.use('/admin-api', adminApiRoutes);

// 系统配置路由
import systemConfigRoutes from './routes/system-config.routes';
app.use('/config', systemConfigRoutes);

// 管理员UI路由
import adminUiRoutes from './routes/admin-ui.routes';
app.use('/admin', adminUiRoutes);

// 审计查询路由
import auditQueryRoutes from './routes/audit-query.routes';
app.use('/audit', auditQueryRoutes);

// 实时监控路由
import monitorRoutes from './routes/monitor.routes';
app.use('/monitor', monitorRoutes);

// 细粒度权限控制路由
import fineGrainedPermissionRoutes from './routes/fine-grained-permission.routes';
app.use('/fine-grained-permission', fineGrainedPermissionRoutes);

// 权限监控路由
import permissionMonitoringRoutes from './routes/permission-monitoring.routes';
app.use('/permission-monitoring', permissionMonitoringRoutes);

// OpenID Connect Provider路由 (原有实现)
import oidcRoutes from './routes/oidc.routes';
app.use('/oauth2/legacy', oidcRoutes);

// 新的 OpenID Connect Provider 路由 (基于 node-oidc-provider)
import oidcProviderRoutes from './routes/oidc-provider.routes';
app.use('/', oidcProviderRoutes);

// SAML 2.0 Identity Provider路由
import samlRoutes from './routes/saml.routes';
app.use('/saml', samlRoutes);

// 协议适配器管理路由
import adapterRoutes from './routes/adapter.routes';
app.use('/api/v1/adapters', adapterRoutes);

// 缓存管理路由
import cacheRoutes from './routes/cache.routes';
app.use('/api/v1/cache', cacheRoutes);

// 数据库优化路由
import databaseOptimizationRoutes from './routes/database-optimization.routes';
app.use('/api/v1/database', databaseOptimizationRoutes);

// 性能测试路由
import performanceTestRoutes from './routes/performance-test.routes';
app.use('/api/v1/performance-test', performanceTestRoutes);

// 性能优化路由
import performanceRoutes from './routes/performance.routes';
app.use('/api/v1/performance', performanceRoutes);

// CDN管理路由
import cdnRoutes from './routes/cdn.routes';
app.use('/api/v1/cdn', cdnRoutes);

// 安全管理路由
import securityRoutes from './routes/security.routes';
app.use('/api/v1/security', securityRoutes);

// 零信任架构路由
import zeroTrustRoutes from './routes/zero-trust.routes';
app.use('/api/v1/zero-trust', zeroTrustRoutes);

// 国际化路由
import i18nRoutes from './routes/i18n.routes';
app.use('/api/v1/i18n', i18nRoutes);

// 移动端路由
import mobileRoutes from './routes/mobile.routes';
app.use('/api/v1/mobile', mobileRoutes);

// 分析和报告路由
import analyticsRoutes from './routes/analytics.routes';
app.use('/api/v1/analytics', analyticsRoutes);

// 监控路由
import monitoringRoutes from './routes/monitoring.routes';
app.use('/api/v1/monitoring', monitoringRoutes);

// 管理员路由
import adminRoutes from './routes/admin.routes';
app.use('/api/v1/admin', adminRoutes);

// Prometheus路由
import prometheusRoutes from './routes/prometheus.routes';
app.use('/', prometheusRoutes);

// 非标准应用路由
import { nonStandardAppRoutes, initializeServices } from './routes/non-standard-app.routes';
app.use('/nsa', nonStandardAppRoutes);

// UI路由 - 必须在API路由之后，404处理之前
app.use('/', uiRoutes);

/**
 * 404处理
 */
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'not_found',
    message: '请求的资源不存在',
    path: req.originalUrl
  });
});

/**
 * 错误日志收集中间件
 */
app.use(errorLogMiddleware);

/**
 * 全局错误处理中间件
 */
app.use((error: Error, req: express.Request, res: express.Response, _next: express.NextFunction) => {
  logger.error('未处理的错误', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip
  });

  // 不泄露内部错误信息到客户端
  if (config.server.nodeEnv === 'production') {
    res.status(500).json({
      error: 'internal_server_error',
      message: '服务器内部错误'
    });
  } else {
    res.status(500).json({
      error: 'internal_server_error',
      message: error.message,
      stack: error.stack
    });
  }
});

/**
 * 启动服务器
 */
async function startServer(): Promise<void> {
  try {
    // 连接数据库
    await connectDatabase();
    logger.info('数据库连接成功');

    // 初始化Redis服务
    try {
      await redisService.initialize();
      logger.info('Redis服务初始化完成');

      // 初始化Redis相关服务
      const { sessionManager } = await import('./services/session-manager.service');
      const { jwtBlacklistService } = await import('./services/jwt-blacklist.service');
      const { redisHealthService } = await import('./services/redis-health.service');
      const { cacheManager } = await import('./services/cache-manager.service');

      // 启动Redis健康监控
      redisHealthService.start();

      // 设置健康检查事件监听
      redisHealthService.on('alert', (result) => {
        logger.warn('Redis健康告警', {
          status: result.status,
          issues: result.issues,
          recommendations: result.recommendations
        });
      });

      redisHealthService.on('statusChange', (event) => {
        logger.info('Redis状态变化', {
          from: event.from,
          to: event.to,
          timestamp: event.result.timestamp
        });
      });

      logger.info('Redis缓存系统初始化完成', {
        services: ['redis', 'session-manager', 'jwt-blacklist', 'health-monitor', 'cache-manager']
      });

      // 初始化数据库优化服务
      const { queryAnalyzer } = await import('./services/query-analyzer.service');
      const { connectionPool } = await import('./services/connection-pool.service');

      // 启动查询分析器
      queryAnalyzer.on('slowQuery', (analysis) => {
        logger.warn('慢查询检测', {
          query: analysis.query.substring(0, 100) + '...',
          duration: analysis.duration,
          severity: analysis.severity
        });
      });

      queryAnalyzer.on('nPlusOneDetected', (detection) => {
        logger.warn('N+1查询检测', {
          pattern: detection.pattern,
          occurrences: detection.occurrences
        });
      });

      logger.info('数据库优化系统初始化完成', {
        services: ['query-analyzer', 'connection-pool', 'prisma-optimizer', 'index-manager']
      });

      // 初始化监控和日志系统
      const { metricsCollector } = await import('./services/metrics-collector.service');
      const { logAggregator } = await import('./services/log-aggregator.service');
      const { alertManager } = await import('./services/alert-manager.service');
      const { systemHealth } = await import('./services/system-health.service');

      // 配置告警管理器
      alertManager.configureNotifications({
        email: {
          host: process.env.SMTP_HOST || 'localhost',
          port: parseInt(process.env.SMTP_PORT || '587'),
          secure: process.env.SMTP_SECURE === 'true',
          auth: {
            user: process.env.SMTP_USER || '',
            pass: process.env.SMTP_PASS || ''
          },
          from: process.env.SMTP_FROM || '<EMAIL>',
          to: (process.env.ALERT_EMAIL_TO || '').split(',').filter(Boolean)
        }
      });

      // 设置事件监听
      systemHealth.on('healthCheck', (health) => {
        metricsCollector.setGauge('system_health_score',
          health.overall === 'healthy' ? 1 :
          health.overall === 'degraded' ? 0.7 :
          health.overall === 'unhealthy' ? 0.3 : 0
        );
      });

      alertManager.on('alertCreated', (alert) => {
        logger.warn('系统告警', {
          alertId: alert.id,
          title: alert.title,
          severity: alert.severity,
          source: alert.source
        });

        metricsCollector.incrementCounter('alerts_created_total', 1, {
          severity: alert.severity,
          source: alert.source
        });
      });

      logger.info('监控和日志系统初始化完成', {
        services: ['metrics-collector', 'log-aggregator', 'alert-manager', 'system-health']
      });

    } catch (error) {
      logger.warn('Redis服务初始化失败，将在无缓存模式下运行', {
        error: error instanceof Error ? error.message : String(error)
      });
    }

    // 初始化Passport策略（在Redis初始化后）
    try {
      await import('@/config/passport');
      logger.info('Passport策略初始化完成');
    } catch (error) {
      logger.error('Passport策略初始化失败', { error });
    }

    // 初始化性能监控
    initializePerformanceMonitoring();

    // 初始化国际化服务
    try {
      await i18nService.initialize();
      logger.info('国际化服务初始化完成');
    } catch (error) {
      logger.error('国际化服务初始化失败', { error });
    }

    // 初始化自动化安全扫描服务
    try {
      const { automatedSecurityScannerService } = await import('./services/automated-security-scanner.service');
      await automatedSecurityScannerService.start();
      logger.info('自动化安全扫描服务初始化完成');
    } catch (error) {
      logger.error('自动化安全扫描服务初始化失败', { error });
    }

    // 初始化自动化性能测试服务
    try {
      const { automatedPerformanceTestingService } = await import('./services/automated-performance-testing.service');
      await automatedPerformanceTestingService.start();
      logger.info('自动化性能测试服务初始化完成');
    } catch (error) {
      logger.error('自动化性能测试服务初始化失败', { error });
    }

    // 初始化自动化性能测试服务
    try {
      const { automatedPerformanceTestingService } = await import('./services/automated-performance-testing.service');
      await automatedPerformanceTestingService.start();
      logger.info('自动化性能测试服务初始化完成');
    } catch (error) {
      logger.error('自动化性能测试服务初始化失败', { error });
    }

    // 初始化非标准应用服务
    try {
      await initializeServices();
      logger.info('非标准应用服务初始化完成');
    } catch (error) {
      logger.error('非标准应用服务初始化失败', { error });
    }

    // 启动HTTP服务器
    const server = app.listen(config.server.port, () => {
      logger.info('服务器启动成功', {
        port: config.server.port,
        nodeEnv: config.server.nodeEnv,
        pid: process.pid
      });

      console.log(`🚀 身份提供商服务运行在端口 ${config.server.port}`);
      console.log(`📖 API文档: http://localhost:${config.server.port}/api/docs`);
      console.log(`🔍 健康检查: http://localhost:${config.server.port}/health`);
      console.log(`📊 性能监控: http://localhost:${config.server.port}/api/v1/monitoring/metrics`);
      console.log(`🔧 非标准应用API: http://localhost:${config.server.port}/nsa`);
    });

    // 优雅关闭处理
    const gracefulShutdown = async (signal: string) => {
      logger.info(`收到${signal}信号，开始优雅关闭...`);

      server.close(async () => {
        logger.info('HTTP服务器已关闭');

        // 停止自动化安全扫描服务
        try {
          const { automatedSecurityScannerService } = await import('./services/automated-security-scanner.service');
          await automatedSecurityScannerService.stop();
          logger.info('自动化安全扫描服务已停止');
        } catch (error) {
          logger.error('停止自动化安全扫描服务失败', { error });
        }

        // 停止自动化性能测试服务
        try {
          const { automatedPerformanceTestingService } = await import('./services/automated-performance-testing.service');
          await automatedPerformanceTestingService.stop();
          logger.info('自动化性能测试服务已停止');
        } catch (error) {
          logger.error('停止自动化性能测试服务失败', { error });
        }

        // 关闭Redis连接
        try {
          await redisService.close();
          logger.info('Redis连接已关闭');
        } catch (error) {
          logger.error('关闭Redis连接失败', { error });
        }

        process.exit(0);
      });

      // 强制关闭超时
      setTimeout(() => {
        logger.error('强制关闭服务器');
        process.exit(1);
      }, 10000);
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // 未捕获异常处理
    process.on('uncaughtException', (error) => {
      logger.error('未捕获的异常', { error });
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('未处理的Promise拒绝', { reason, promise });
      process.exit(1);
    });

  } catch (error) {
    logger.error('服务器启动失败', { error });
    process.exit(1);
  }
}

// 启动应用
if (require.main === module) {
  startServer().catch((error) => {
    logger.error('应用启动失败', { error });
    process.exit(1);
  });
}

export default app;
