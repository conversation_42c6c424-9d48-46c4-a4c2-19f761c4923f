# ID Provider 应用接入指南

## 📋 文档概览

**版本**: 2.0
**更新日期**: 2025-08-28
**适用范围**: 组织架构权限控制系统
**目标读者**: 应用开发者、系统集成工程师

---

## 🎯 接入概述

ID Provider 现已支持完整的组织架构权限控制系统，为应用提供：
- 多层级组织架构管理
- 基于组织的权限继承和隔离
- 跨组织权限申请和审批
- 细粒度权限控制
- 实时权限验证

---

## 🚀 快速开始

### 1. 应用注册

首先在 ID Provider 中注册您的应用：

```bash
curl -X POST "https://id-provider.example.com/api/v1/applications" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "my-app",
    "displayName": "我的应用",
    "description": "应用描述",
    "redirectUris": ["https://my-app.com/callback"],
    "organizationScoped": true,
    "supportedPermissions": [
      "read:users",
      "write:users",
      "read:data",
      "write:data",
      "admin:settings"
    ]
  }'
```

### 2. 获取应用凭证

注册成功后，您将获得：
- `client_id`: 应用客户端ID
- `client_secret`: 应用客户端密钥
- `organization_id`: 应用所属组织ID

---

## 🏗️ 组织架构集成

### 1. 组织架构概念

```
企业 (Company)
├── 事业部 (Division)
│   ├── 部门 (Department)
│   │   ├── 团队 (Team)
│   │   └── 项目组 (Project)
│   └── 分部 (Branch)
└── 子公司 (Subsidiary)
```

### 2. 组织路径格式

组织使用点分隔的路径格式：
- `acme` - 根组织
- `acme.engineering` - 工程部
- `acme.engineering.backend` - 后端团队
- `acme.engineering.backend.auth` - 认证小组

### 3. 创建组织

```javascript
// 创建组织
const createOrganization = async (organizationData) => {
  const response = await fetch('/api/v1/organizations', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      name: 'backend',
      displayName: '后端团队',
      description: '负责后端服务开发',
      parentId: 'parent-org-id', // 父组织ID
      type: 'team',
      permissionInheritance: true,
      dataIsolationLevel: 'inherit'
    })
  });
  
  return response.json();
};
```

---

## 🔐 权限管理集成

### 1. 权限信息上报

应用需要向 ID Provider 上报其权限信息：

```javascript
// 上报权限元数据
const reportPermissions = async () => {
  const permissions = [
    {
      name: 'read:users',
      displayName: '查看用户',
      description: '查看用户基本信息',
      category: 'user_management',
      scope: 'application',
      type: 'data',
      level: 'read'
    },
    {
      name: 'write:users',
      displayName: '管理用户',
      description: '创建、编辑、删除用户',
      category: 'user_management',
      scope: 'application',
      type: 'data',
      level: 'write',
      dependencies: ['read:users']
    },
    {
      name: 'admin:settings',
      displayName: '系统设置',
      description: '管理系统配置',
      category: 'system',
      scope: 'application',
      type: 'admin',
      level: 'admin'
    }
  ];

  const response = await fetch('/permissions/register', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      applicationId: 'your-app-id',
      permissions
    })
  });

  return response.json();
};
```

### 2. 权限验证中间件

在您的应用中集成权限验证：

```javascript
// Express.js 中间件示例
const checkOrganizationPermission = (permission, options = {}) => {
  return async (req, res, next) => {
    try {
      const token = req.headers.authorization?.replace('Bearer ', '');
      const organizationId = req.params.organizationId || req.body.organizationId;
      
      // 验证权限
      const response = await fetch('/api/v1/permissions/validate', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          permission,
          organizationId,
          resourceType: options.resourceType,
          resourceId: req.params.resourceId
        })
      });

      const result = await response.json();
      
      if (!result.success || !result.data.granted) {
        return res.status(403).json({
          error: 'INSUFFICIENT_PERMISSION',
          message: '权限不足',
          required: permission
        });
      }

      // 设置组织上下文
      req.organizationContext = result.data;
      next();
    } catch (error) {
      res.status(500).json({
        error: 'PERMISSION_CHECK_FAILED',
        message: '权限验证失败'
      });
    }
  };
};

// 使用示例
app.get('/api/users/:organizationId', 
  checkOrganizationPermission('read:users'),
  (req, res) => {
    // 处理请求
    const { organizationContext } = req;
    // organizationContext 包含用户的组织权限信息
  }
);
```

### 3. 数据访问过滤

基于组织架构过滤数据：

```javascript
// 数据查询过滤示例
const getFilteredData = async (userId, query) => {
  // 获取用户的组织权限
  const response = await fetch(`/api/v1/users/${userId}/organizations`, {
    headers: {
      'Authorization': `Bearer ${accessToken}`
    }
  });
  
  const userOrgs = await response.json();
  
  // 构建组织过滤条件
  const accessibleOrgPaths = userOrgs.data
    .map(org => org.organization.path)
    .concat(
      userOrgs.data
        .filter(org => org.permissions.includes('read:descendants'))
        .map(org => `${org.organization.path}.*`)
    );

  // 应用过滤条件到数据库查询
  const filteredQuery = {
    ...query,
    organizationPath: {
      $in: accessibleOrgPaths
    }
  };

  return await database.find(filteredQuery);
};
```

---

## 🔄 权限申请流程

### 1. 发起权限申请

```javascript
// 申请跨组织权限
const requestPermission = async (requestData) => {
  const response = await fetch('/api/v1/permission-requests', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      targetOrganizationId: 'target-org-id',
      requestedPermissions: ['read:data', 'write:reports'],
      requestType: 'project',
      reason: '需要访问销售数据进行项目分析',
      businessJustification: '季度报告项目需要跨部门数据支持',
      priority: 'normal',
      requestedDuration: 168 // 7天（小时）
    })
  });

  return response.json();
};
```

### 2. 处理权限申请

```javascript
// 审批权限申请
const approveRequest = async (requestId, approvalData) => {
  const response = await fetch(`/api/v1/permission-requests/${requestId}/approve`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      approvedPermissions: ['read:data'], // 可以部分批准
      approvedDuration: 72, // 3天
      conditions: {
        ipRestriction: ['***********/24'],
        timeRestriction: {
          start: '09:00',
          end: '18:00'
        }
      },
      comments: '批准读取权限，限制IP和时间'
    })
  });

  return response.json();
};

// 拒绝权限申请
const rejectRequest = async (requestId, reason) => {
  const response = await fetch(`/api/v1/permission-requests/${requestId}/reject`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      reason: '申请理由不充分，请提供更详细的业务需求',
      comments: '建议通过数据报告获取所需信息'
    })
  });

  return response.json();
};
```

### 3. 监听权限变更

```javascript
// WebSocket 连接监听权限变更
const ws = new WebSocket('wss://id-provider.example.com/ws/permissions');

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  
  switch (data.type) {
    case 'PERMISSION_GRANTED':
      console.log('权限已授予:', data.permissions);
      // 刷新用户权限缓存
      refreshUserPermissions(data.userId);
      break;
      
    case 'PERMISSION_REVOKED':
      console.log('权限已撤销:', data.permissions);
      // 清除相关缓存，重新验证权限
      clearPermissionCache(data.userId);
      break;
      
    case 'ORGANIZATION_UPDATED':
      console.log('组织架构已更新:', data.organization);
      // 更新组织信息缓存
      updateOrganizationCache(data.organization);
      break;
  }
};
```

---

## 📊 权限数据格式规范

### 1. 权限元数据格式

```typescript
interface PermissionMetadata {
  id: string;
  name: string;                    // 权限标识符，如 "read:users"
  displayName: string;             // 显示名称
  description: string;             // 权限描述
  category: string;                // 权限分类
  scope: 'global' | 'application' | 'resource'; // 权限作用域
  type: 'action' | 'data' | 'feature' | 'admin'; // 权限类型
  level: 'read' | 'write' | 'admin' | 'owner';   // 权限级别
  dependencies: string[];          // 依赖的权限
  conflicts: string[];             // 冲突的权限
  conditions?: {                   // 权限条件
    timeRestriction?: {
      start: string;
      end: string;
    };
    ipRestriction?: string[];
    deviceRestriction?: string[];
  };
}
```

### 2. 组织权限上下文

```typescript
interface OrganizationContext {
  organizationId: string;
  organizationPath: string;        // 如 "acme.engineering.backend"
  userRole: string;               // 用户在组织中的角色
  permissions: string[];          // 有效权限列表
  inheritanceChain?: string[];    // 权限继承链
  restrictions?: {                // 权限限制
    ipAddresses?: string[];
    timeWindows?: TimeWindow[];
    conditions?: Record<string, any>;
  };
}
```

---

## 🔧 SDK 集成

### JavaScript/Node.js SDK

```bash
npm install @id-provider/sdk
```

```javascript
import { IDProviderClient } from '@id-provider/sdk';

const client = new IDProviderClient({
  baseURL: 'https://id-provider.example.com',
  clientId: 'your-client-id',
  clientSecret: 'your-client-secret'
});

// 验证权限
const hasPermission = await client.permissions.check({
  userId: 'user-id',
  permission: 'read:users',
  organizationId: 'org-id'
});

// 获取用户组织
const userOrgs = await client.organizations.getUserOrganizations('user-id');

// 申请权限
const request = await client.permissions.request({
  targetOrganizationId: 'target-org-id',
  permissions: ['read:data'],
  reason: '业务需要'
});
```

### Python SDK

```bash
pip install id-provider-sdk
```

```python
from id_provider_sdk import IDProviderClient

client = IDProviderClient(
    base_url='https://id-provider.example.com',
    client_id='your-client-id',
    client_secret='your-client-secret'
)

# 验证权限
has_permission = client.permissions.check(
    user_id='user-id',
    permission='read:users',
    organization_id='org-id'
)

# 获取用户组织
user_orgs = client.organizations.get_user_organizations('user-id')
```

---

## 🚨 错误处理

### 常见错误码

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| `INSUFFICIENT_PERMISSION` | 权限不足 | 检查用户权限或申请相应权限 |
| `ORGANIZATION_NOT_FOUND` | 组织不存在 | 验证组织ID是否正确 |
| `INVALID_PERMISSION_FORMAT` | 权限格式无效 | 检查权限名称格式 |
| `PERMISSION_REQUEST_DENIED` | 权限申请被拒绝 | 查看拒绝原因，重新申请 |
| `CROSS_ORG_ACCESS_DENIED` | 跨组织访问被拒绝 | 申请跨组织访问权限 |

### 错误处理示例

```javascript
try {
  const result = await client.permissions.check({
    userId: 'user-id',
    permission: 'admin:settings',
    organizationId: 'org-id'
  });
} catch (error) {
  switch (error.code) {
    case 'INSUFFICIENT_PERMISSION':
      // 引导用户申请权限
      showPermissionRequestDialog(error.details.requiredPermission);
      break;
    case 'ORGANIZATION_NOT_FOUND':
      // 显示组织不存在错误
      showError('组织不存在，请检查组织ID');
      break;
    default:
      // 通用错误处理
      showError('权限验证失败，请稍后重试');
  }
}
```

---

## 📈 性能优化建议

### 1. 权限缓存

```javascript
// 实现权限缓存
class PermissionCache {
  constructor() {
    this.cache = new Map();
    this.ttl = 5 * 60 * 1000; // 5分钟
  }

  async checkPermission(userId, permission, organizationId) {
    const key = `${userId}:${permission}:${organizationId}`;
    const cached = this.cache.get(key);
    
    if (cached && Date.now() - cached.timestamp < this.ttl) {
      return cached.result;
    }

    const result = await this.fetchPermission(userId, permission, organizationId);
    this.cache.set(key, {
      result,
      timestamp: Date.now()
    });

    return result;
  }
}
```

### 2. 批量权限检查

```javascript
// 批量检查权限
const checkMultiplePermissions = async (userId, permissions, organizationId) => {
  const response = await fetch('/api/v1/permissions/batch-check', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      userId,
      permissions,
      organizationId
    })
  });

  return response.json();
};
```

---

---

## 🔗 相关文档

- [权限申请流程指南](./权限申请流程指南.md)
- [组织架构权限控制使用指南](./组织架构权限控制使用指南.md)
- [API 参考文档](./API参考文档.md)
- [SDK 开发指南](./SDK开发指南.md)

## 📞 技术支持

- **文档**: https://docs.id-provider.example.com
- **API 文档**: https://api.id-provider.example.com/docs
- **技术支持**: <EMAIL>
- **GitHub**: https://github.com/your-org/id-provider

---

*本文档将根据系统更新持续维护，如有疑问请联系技术支持团队。*
